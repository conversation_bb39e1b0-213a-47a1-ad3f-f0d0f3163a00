"use client";

import { useEffect, useState } from "react";
import { getUserOrders } from "@/api/order-api";
import { OrderEntity } from "@/core.constants";
import { useRootContext } from "@/root-context";
import { Loader2 } from "lucide-react";
import { VirtualizedUserOrderCard } from "./virtualized-user-order-card";
import { UserOrderDetailsDrawer } from "./user-order-details-drawer";
import { ItemCacheProvider } from "@/components/ui/virtualized-grid";

export default function OrdersPage() {
  const { currentUser } = useRootContext();
  const [orders, setOrders] = useState<OrderEntity[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);

  const fetchUserOrders = async () => {
    if (!currentUser?.id) return;

    setLoading(true);
    try {
      const userOrders = await getUserOrders(currentUser.id);
      setOrders(userOrders);
    } catch (error) {
      console.error("Error fetching user orders:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserOrders();
  }, [currentUser?.id]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderUpdate = () => {
    fetchUserOrders();
  };

  // Get user role for each order
  const getUserRole = (order: OrderEntity): "seller" | "buyer" => {
    return order.sellerId === currentUser?.id ? "seller" : "buyer";
  };

  if (!currentUser) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-[#708499]">Please log in to view your orders</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4 pb-[75px]">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">My Orders</h1>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin text-[#6ab2f2]" />
        </div>
      ) : orders.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-[#708499]">No orders found</p>
        </div>
      ) : (
        <ItemCacheProvider>
          <div className="grid grid-cols-2 gap-3">
            {orders.map((order, index) => (
              <VirtualizedUserOrderCard
                key={order.id}
                order={order}
                userRole={getUserRole(order)}
                onClick={() => handleOrderClick(order)}
                index={index}
                initialRenderedCount={8}
              />
            ))}
          </div>
        </ItemCacheProvider>
      )}

      {/* Order Details Drawer */}
      <UserOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        userRole={selectedOrder ? getUserRole(selectedOrder) : "buyer"}
        onOrderUpdate={handleOrderUpdate}
      />
    </div>
  );
}
