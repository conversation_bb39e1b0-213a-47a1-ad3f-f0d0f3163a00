"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { OrderEntity } from "@/core.constants";
import { firebaseFunctions, useRootContext } from "@/root-context";
import { httpsCallable } from "firebase/functions";
import { Loader2, AlertTriangle } from "lucide-react";
import { toast } from "sonner";
import { Drawer } from "vaul";

interface CancelOrderDrawerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  order: OrderEntity | null;
  onOrderCancelled: () => void;
}

export function CancelOrderDrawer({
  open,
  onOpenChange,
  order,
  onOrderCancelled,
}: CancelOrderDrawerProps) {
  const { currentUser } = useRootContext();
  const [cancelling, setCancelling] = useState(false);

  const handleCancelOrder = async () => {
    if (!order || !currentUser) return;

    setCancelling(true);
    try {
      const cancelOrderFunction = httpsCallable(firebaseFunctions, "cancelOrder");
      const result = await cancelOrderFunction({
        orderId: order.id,
        userId: currentUser.id,
      });

      const data = result.data as { success: boolean; message: string };
      
      if (data.success) {
        toast.success("Order cancelled successfully");
        onOrderCancelled();
        onOpenChange(false);
      } else {
        toast.error(data.message || "Failed to cancel order");
      }
    } catch (error: any) {
      console.error("Error cancelling order:", error);
      toast.error(error.message || "Failed to cancel order");
    } finally {
      setCancelling(false);
    }
  };

  if (!order) return null;

  return (
    <Drawer.Root open={open} onOpenChange={onOpenChange}>
      <Drawer.Portal>
        <Drawer.Overlay className="fixed inset-0 bg-black/40 z-50" />
        <Drawer.Content className="bg-[#232e3c] flex flex-col rounded-t-[10px] h-auto mt-24 fixed bottom-0 left-0 right-0 z-50">
          <div className="p-6 bg-[#232e3c] rounded-t-[10px]">
            <div className="mx-auto w-12 h-1.5 flex-shrink-0 rounded-full bg-[#3a4a5c] mb-6" />
            
            <div className="space-y-6">
              {/* Header */}
              <div className="text-center">
                <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <AlertTriangle className="w-8 h-8 text-red-400" />
                </div>
                <h2 className="text-xl font-bold text-[#f5f5f5] mb-2">Cancel Order</h2>
                <p className="text-[#708499] text-sm">
                  Order #{order.number || order.id?.slice(-6)}
                </p>
              </div>

              {/* Warning Message */}
              <div className="p-4 bg-red-500/10 border border-red-500/20 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="w-5 h-5 text-red-400 flex-shrink-0 mt-0.5" />
                  <div>
                    <h3 className="text-red-400 font-medium mb-2">Warning: Funds Will Be Lost</h3>
                    <p className="text-[#708499] text-sm">
                      If you cancel this order, all locked funds will be lost and cannot be recovered. 
                      This action is permanent and cannot be undone.
                    </p>
                  </div>
                </div>
              </div>

              {/* Order Details */}
              <div className="space-y-3 p-4 bg-[#17212b] rounded-lg">
                <div className="flex justify-between">
                  <span className="text-[#708499]">Order Amount</span>
                  <span className="text-[#f5f5f5] font-medium">{order.amount} TON</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#708499]">Status</span>
                  <span className="text-[#f5f5f5] capitalize">{order.status.replace('_', ' ')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-[#708499]">Collection</span>
                  <span className="text-[#f5f5f5]">#{order.collectionId}</span>
                </div>
              </div>

              {/* Confirmation Text */}
              <div className="text-center">
                <p className="text-[#708499] text-sm">
                  Are you sure you want to cancel this order?
                </p>
              </div>

              {/* Actions */}
              <div className="flex gap-3">
                <Button
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                  disabled={cancelling}
                  className="flex-1 border-[#3a4a5c] text-[#f5f5f5] hover:bg-[#3a4a5c]"
                >
                  Keep Order
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleCancelOrder}
                  disabled={cancelling}
                  className="flex-1"
                >
                  {cancelling ? (
                    <>
                      <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                      Cancelling...
                    </>
                  ) : (
                    "Cancel Order"
                  )}
                </Button>
              </div>
            </div>
          </div>
        </Drawer.Content>
      </Drawer.Portal>
    </Drawer.Root>
  );
}
