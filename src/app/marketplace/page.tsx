"use client";


import { But<PERSON> as TgButton } from "@telegram-apps/telegram-ui";
import { useCallback, useEffect, useRef, useState } from "react";

import { getOrdersForBuyers, getOrdersForSellers, clearOrdersCache } from "@/api/order-api";
import { CollectionSelect } from "@/components/ui/collection-select";
import { Tabs, TabsContent } from "@/components/ui/tabs";
import { OrderEntity } from "@/core.constants";
import { DocumentSnapshot } from "firebase/firestore";
import { useRootContext } from "@/root-context";
import {
  Select,
  Spinner,
  TabsList,
  Input as TgInput,
} from "@telegram-apps/telegram-ui";
import { TabsItem } from "@telegram-apps/telegram-ui/dist/components/Navigation/TabsList/components/TabsItem/TabsItem";
import { Loader2, Plus } from "lucide-react";
import { CreateOrderDrawer } from "./create-order-drawer";
import { VirtualizedOrderCard } from "./virtualized-order-card";
import { OrderDetailsDrawer } from "./order-details-drawer";
import { ItemCacheProvider } from "@/components/ui/virtualized-grid";
import { useInfiniteScroll } from "@/hooks/useInfiniteScroll";

export default function MarketplacePage() {
  const { collections, refetchUser } = useRootContext();
  const [activeTab, setActiveTab] = useState<"sellers" | "buyers">("sellers");
  const [showCreateOrderDrawer, setShowCreateOrderDrawer] = useState(false);
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  // Separate state for each tab to prevent duplicate keys and improve UX
  const [sellersOrders, setSellersOrders] = useState<OrderEntity[]>([]);
  const [buyersOrders, setBuyersOrders] = useState<OrderEntity[]>([]);
  const [sellersLoading, setSellersLoading] = useState(false);
  const [buyersLoading, setBuyersLoading] = useState(false);
  const [sellersLoadingMore, setSellersLoadingMore] = useState(false);
  const [buyersLoadingMore, setBuyersLoadingMore] = useState(false);
  const [sellersHasMore, setSellersHasMore] = useState(true);
  const [buyersHasMore, setBuyersHasMore] = useState(true);
  const [sellersLastDoc, setSellersLastDoc] = useState<DocumentSnapshot | null>(
    null
  );
  const [buyersLastDoc, setBuyersLastDoc] = useState<DocumentSnapshot | null>(
    null
  );

  // Use refs to store current lastDoc values to avoid stale state issues
  const sellersLastDocRef = useRef<DocumentSnapshot | null>(null);
  const buyersLastDocRef = useRef<DocumentSnapshot | null>(null);

  // Filters
  const [minPrice, setMinPrice] = useState("");
  const [maxPrice, setMaxPrice] = useState("");
  const [selectedCollection, setSelectedCollection] = useState<string>("all");
  const [sortBy, setSortBy] = useState<
    "price_asc" | "price_desc" | "date_asc" | "date_desc"
  >("date_desc");



  const loadOrders = useCallback(
    async (reset = true) => {
      console.log("🔄 loadOrders called with reset:", reset);
      const isSellersTab = activeTab === "sellers";

      if (reset) {
        if (isSellersTab) {
          setSellersLoading(true);
          setSellersOrders([]);
          setSellersLastDoc(null);
          sellersLastDocRef.current = null;
          setSellersHasMore(true);
        } else {
          setBuyersLoading(true);
          setBuyersOrders([]);
          setBuyersLastDoc(null);
          buyersLastDocRef.current = null;
          setBuyersHasMore(true);
        }
      } else {
        if (isSellersTab) {
          setSellersLoadingMore(true);
        } else {
          setBuyersLoadingMore(true);
        }
      }

      try {
        // For reset, always use null. For pagination, use ref values to avoid stale state
        const currentLastDoc = reset
          ? null
          : isSellersTab
          ? sellersLastDocRef.current
          : buyersLastDocRef.current;

        console.log("🔍 loadOrders debug:", {
          reset,
          isSellersTab,
          sellersLastDoc: sellersLastDoc?.id || 'null',
          buyersLastDoc: buyersLastDoc?.id || 'null',
          sellersLastDocRef: sellersLastDocRef.current?.id || 'null',
          buyersLastDocRef: buyersLastDocRef.current?.id || 'null',
          currentLastDoc: currentLastDoc?.id || 'null',
          ordersCount: isSellersTab ? sellersOrders.length : buyersOrders.length
        });

        // Ensure sortBy is a valid string value
        const validSortBy =
          typeof sortBy === "string" &&
          ["price_asc", "price_desc", "date_asc", "date_desc"].includes(sortBy)
            ? sortBy
            : "date_desc";

        const filters = {
          minPrice: minPrice ? parseFloat(minPrice) : undefined,
          maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
          collectionId:
            selectedCollection !== "all" ? selectedCollection : undefined,
          sortBy: validSortBy,
          limit: 3,
          lastDoc: currentLastDoc,
        };

        let result;
        if (isSellersTab) {
          // For sellers tab: get orders where buyerId !== null and sellerId === null
          result = await getOrdersForSellers(filters);
        } else {
          // For buyers tab: get orders where sellerId !== null and buyerId === null
          result = await getOrdersForBuyers(filters);
        }

        if (isSellersTab) {
          if (reset) {
            console.log(
              "🔄 Resetting sellers orders with",
              result.orders.length,
              "orders"
            );
            setSellersOrders(result.orders);
          } else {
            // Deduplicate orders to prevent duplicate keys
            setSellersOrders((prev) => {
              const existingIds = new Set(prev.map((order) => order.id));
              const newOrders = result.orders.filter(
                (order) => !existingIds.has(order.id)
              );
              const duplicates = result.orders.filter((order) =>
                existingIds.has(order.id)
              );

              if (duplicates.length > 0) {
                console.warn(
                  "🚨 Found duplicate orders for sellers:",
                  duplicates.map((o) => o.id)
                );
              }

              console.log(
                "➕ Adding",
                newOrders.length,
                "new sellers orders, filtered out",
                duplicates.length,
                "duplicates"
              );
              return [...prev, ...newOrders];
            });
          }
          setSellersLastDoc(result.lastDoc);
          sellersLastDocRef.current = result.lastDoc;
          setSellersHasMore(result.hasMore);
        } else {
          if (reset) {
            console.log(
              "🔄 Resetting buyers orders with",
              result.orders.length,
              "orders"
            );
            setBuyersOrders(result.orders);
          } else {
            // Deduplicate orders to prevent duplicate keys
            setBuyersOrders((prev) => {
              const existingIds = new Set(prev.map((order) => order.id));
              const newOrders = result.orders.filter(
                (order) => !existingIds.has(order.id)
              );
              const duplicates = result.orders.filter((order) =>
                existingIds.has(order.id)
              );

              if (duplicates.length > 0) {
                console.warn(
                  "🚨 Found duplicate orders for buyers:",
                  duplicates.map((o) => o.id)
                );
              }

              console.log(
                "➕ Adding",
                newOrders.length,
                "new buyers orders, filtered out",
                duplicates.length,
                "duplicates"
              );
              return [...prev, ...newOrders];
            });
          }
          console.log("🔍 Setting buyersLastDoc:", result.lastDoc?.id || 'null');
          setBuyersLastDoc(result.lastDoc);
          buyersLastDocRef.current = result.lastDoc;
          setBuyersHasMore(result.hasMore);
        }
      } catch (error) {
        console.error("Error loading orders:", error);
      } finally {
        if (isSellersTab) {
          setSellersLoading(false);
          setSellersLoadingMore(false);
        } else {
          setBuyersLoading(false);
          setBuyersLoadingMore(false);
        }
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [activeTab, minPrice, maxPrice, selectedCollection, sortBy]
  );

  // Single load more function that handles both tabs
  const loadMoreOrders = useCallback(() => {
    console.log("🔄 loadMoreOrders called, activeTab:", activeTab);
    const isSellersTab = activeTab === "sellers";
    const hasMore = isSellersTab ? sellersHasMore : buyersHasMore;
    const loading = isSellersTab ? (sellersLoading || sellersLoadingMore) : (buyersLoading || buyersLoadingMore);

    console.log("� Load more state:", { hasMore, loading, isSellersTab });

    if (hasMore && !loading) {
      console.log("✅ Loading more orders...");
      loadOrders(false);
    } else {
      console.log("❌ Cannot load more:", { hasMore, loading });
    }
  }, [activeTab, sellersHasMore, buyersHasMore, sellersLoading, sellersLoadingMore, buyersLoading, buyersLoadingMore, loadOrders]);

  // Separate infinite scroll hooks for each tab
  const sellersLoadMoreRef = useInfiniteScroll({
    hasMore: sellersHasMore,
    loading: sellersLoading || sellersLoadingMore,
    onLoadMore: () => {
      console.log("🔄 Sellers infinite scroll triggered");
      if (activeTab === "sellers") {
        loadMoreOrders();
      }
    },
  });

  const buyersLoadMoreRef = useInfiniteScroll({
    hasMore: buyersHasMore,
    loading: buyersLoading || buyersLoadingMore,
    onLoadMore: () => {
      console.log("🔄 Buyers infinite scroll triggered");
      if (activeTab === "buyers") {
        loadMoreOrders();
      }
    },
  });



  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeTab, minPrice, maxPrice, selectedCollection, sortBy]);



  const handleCreateOrder = () => {
    setShowCreateOrderDrawer(true);
  };

  const handleOrderCreated = () => {
    clearOrdersCache(); // Clear cache to ensure fresh data
    loadOrders(true); // Refresh orders after creating
    refetchUser(); // Refresh user data to update balance
  };

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    clearOrdersCache(); // Clear cache to ensure fresh data
    loadOrders(true); // Refresh orders after action
    refetchUser(); // Refresh user data to update balance
  };

  return (
    <div className="space-y-2 bg-[#17212b] min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">Marketplace</h1>
        <TgButton
          onClick={handleCreateOrder}
          className="[&>h6]:flex [&>h6]:items-center [&>h6]:gap-1"
        >
          <Plus className="w-4 h-4 stroke-[2.5]" />
          <span>Create</span>
        </TgButton>
      </div>

      <Tabs value={activeTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsItem
            selected={activeTab === "sellers"}
            onClick={() => setActiveTab("sellers")}
          >
            For Sellers
          </TabsItem>
          <TabsItem
            selected={activeTab === "buyers"}
            onClick={() => setActiveTab("buyers")}
          >
            For Buyers
          </TabsItem>
        </TabsList>

        {/* Unified Filters - Single Row */}
        <div className="flex flex-wrap items-end gap-2 py-3 rounded-lg">
          <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
            <div className="[&>div]:p-0!">
              <TgInput
                type="number"
                header="Min"
                placeholder="0"
                value={minPrice}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow empty string or valid numbers (including decimals)
                  if (value === "" || /^\d*\.?\d*$/.test(value)) {
                    setMinPrice(value);
                  }
                }}
                min="0"
                step="0.01"
                className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
              />
            </div>
          </div>
          <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
            <div className="[&>div]:p-0!">
              <TgInput
                type="number"
                header="Max"
                placeholder="0"
                value={maxPrice}
                onChange={(e) => {
                  const value = e.target.value;
                  // Allow empty string or valid numbers (including decimals)
                  if (value === "" || /^\d*\.?\d*$/.test(value)) {
                    setMaxPrice(value);
                  }
                }}
                min="0"
                step="0.01"
                className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
              />
            </div>
          </div>

          <div className="flex-1 min-w-[120px] xss:min-w-[140px] mt-2">
            <CollectionSelect
              animated
              collections={collections}
              value={selectedCollection}
              onValueChange={setSelectedCollection}
              placeholder="All Collections"
            />
          </div>
          <div className="flex-1 min-w-[120px] xss:min-w-[140px] mt-2">
            <div className="[&>div]:p-0!">
              <Select
                header="Sort by"
                value={sortBy}
                onChange={(e) => {
                  const value = e.target.value as
                    | "price_asc"
                    | "price_desc"
                    | "date_asc"
                    | "date_desc";
                  setSortBy(value);
                }}
                className="[&>select]:px-[12px]! [&>select]:py-[6px]! [&+h6]:-top-[12px]!"
              >
                <option value={"date_desc"}>Newest First</option>
                <option value={"date_asc"}>Oldest First</option>
                <option value={"price_desc"}>Price: High to Low</option>
                <option value={"price_asc"}>Price: Low to High</option>
              </Select>
            </div>
          </div>
        </div>

        <TabsContent value="sellers" className="space-y-4">
          {sellersLoading ? (
            <div className="text-center py-8">
              <Spinner className="flex justify-center" size="l" />
              <p className="text-[#708499] mt-2">Loading orders...</p>
            </div>
          ) : sellersOrders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-[#708499]">No orders found for sellers</p>
            </div>
          ) : (
            <ItemCacheProvider>
              <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
                {sellersOrders.map((order, index) => (
                  <VirtualizedOrderCard
                    animated
                    key={`sellers-${order.id}-${index}`}
                    order={order}
                    collection={collections.find(
                      (c) => c.id === order.collectionId
                    )}
                    onClick={() => handleOrderClick(order)}
                    index={index}
                    initialRenderedCount={8}
                  />
                ))}
              </div>
            </ItemCacheProvider>
          )}

          {/* Load more trigger for infinite scroll */}
          <div
            ref={sellersLoadMoreRef}
            className="flex justify-center py-4 w-full"
            style={{
              height: "60px",
              minHeight: "60px",
              backgroundColor: "transparent"
            }}
          >
            {sellersLoadingMore && (
              <div className="flex items-center gap-2 text-gray-400">
                <Spinner className="flex justify-center" size="l" />
                <span>Loading more orders...</span>
              </div>
            )}
          </div>
        </TabsContent>

        <TabsContent value="buyers" className="space-y-4">
          {buyersLoading ? (
            <div className="text-center py-8">
              <Spinner className="flex justify-center" size="l" />
              <p className="text-[#708499] mt-2">Loading orders...</p>
            </div>
          ) : buyersOrders.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-[#708499]">No orders found for buyers</p>
            </div>
          ) : (
            <ItemCacheProvider>
              <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
                {buyersOrders.map((order, index) => (
                  <VirtualizedOrderCard
                    animated
                    key={`buyers-${order.id}-${index}`}
                    order={order}
                    collection={collections.find(
                      (c) => c.id === order.collectionId
                    )}
                    onClick={() => handleOrderClick(order)}
                    index={index}
                    initialRenderedCount={8}
                  />
                ))}
              </div>
            </ItemCacheProvider>
          )}

          {/* Load more trigger for infinite scroll */}
          <div
            ref={buyersLoadMoreRef}
            className="flex justify-center py-4 w-full"
            style={{
              height: "60px",
              minHeight: "60px",
              backgroundColor: "transparent"
            }}
          >
            {buyersLoadingMore && (
              <div className="flex items-center gap-2 text-gray-400">
                <Loader2 className="w-4 h-4 animate-spin" />
                <span>Loading more orders...</span>
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>

      <CreateOrderDrawer
        open={showCreateOrderDrawer}
        onOpenChange={setShowCreateOrderDrawer}
        orderType={activeTab === "sellers" ? "seller" : "buyer"}
        onOrderCreated={handleOrderCreated}
      />

      <OrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        orderType={activeTab === "sellers" ? "seller" : "buyer"}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
