import { mnemonicToPrivate<PERSON><PERSON> } from "@ton/crypto";
import { SendMode, TonClient, WalletContractV5R1, internal } from "@ton/ton";
import * as admin from "firebase-admin";
import { onCall, HttpsError } from "firebase-functions/v2/https";
import {
  hasAvailableBalance,
  spendLockedFunds,
} from "./services/balance-service";
import { getMarketplaceWalletMnemonic, isDevelopment } from "./config";
import { applyWithdrawFee, getAppConfig } from "./services/fee-service";
import { UserEntity } from "./types";
import { getHttpEndpoint } from "@orbs-network/ton-access";
import { safeSubtract } from "./utils";

export const withdrawFunds = onCall(async (request) => {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }

  const { amount } = request.data;

  if (!amount || amount <= 0) {
    throw new HttpsError("invalid-argument", "Valid amount is required.");
  }

  try {
    const db = admin.firestore();
    const userId = request.auth.uid;

    // Get user data
    const userDoc = await db.collection("users").doc(userId).get();
    if (!userDoc.exists) {
      throw new HttpsError("not-found", "User not found.");
    }

    const user = { id: userDoc.id, ...userDoc.data() } as UserEntity;

    // Check if user has a TON wallet address
    if (!user.ton_wallet_address) {
      throw new HttpsError(
        "failed-precondition",
        "User does not have a TON wallet address configured."
      );
    }

    // Get app config to check withdrawal limits
    const appConfig = await getAppConfig();
    if (appConfig) {
      const minWithdrawal = appConfig.min_withdrawal_amount || 0;
      const maxWithdrawal =
        appConfig.max_withdrawal_amount || Number.MAX_SAFE_INTEGER;

      if (amount < minWithdrawal) {
        throw new HttpsError(
          "failed-precondition",
          `Withdrawal amount must be at least ${minWithdrawal} TON.`
        );
      }

      if (amount > maxWithdrawal) {
        throw new HttpsError(
          "failed-precondition",
          `Withdrawal amount cannot exceed ${maxWithdrawal} TON.`
        );
      }
    }

    // Check if user has sufficient available balance
    const hasBalance = await hasAvailableBalance(userId, amount);
    if (!hasBalance) {
      throw new HttpsError(
        "failed-precondition",
        "Insufficient available balance for withdrawal."
      );
    }

    // Apply withdrawal fee and get net amount
    const feeAmount = await applyWithdrawFee(userId, amount);
    const netAmountToUser = safeSubtract(amount, feeAmount);

    if (netAmountToUser <= 0) {
      throw new HttpsError(
        "failed-precondition",
        "Amount too small after fees."
      );
    }

    // Deduct the full amount from user's balance (including fee)
    await spendLockedFunds(userId, amount);

    const network = isDevelopment() ? "testnet" : "mainnet";
    const endpoint = await getHttpEndpoint({ network });

    const client = new TonClient({
      endpoint,
    });

    // Get marketplace wallet mnemonic and create key pair
    const marketplaceMnemonic = getMarketplaceWalletMnemonic();
    const keyPair = await mnemonicToPrivateKey(marketplaceMnemonic.split(" "));

    // Create marketplace wallet contract
    const workchain = 0;
    const marketplaceWallet = WalletContractV5R1.create({
      workchain,
      publicKey: keyPair.publicKey,
    });
    const marketplaceContract = client.open(marketplaceWallet);

    // Get sequence number for the transaction
    const seqno = await marketplaceWallet.getSeqno(
      client.provider(marketplaceWallet.address)
    );

    // Convert amount to nanotons (1 TON = 1,000,000,000 nanotons)
    const value = netAmountToUser;

    // Create transfer transaction
    const transfer = marketplaceContract.createTransfer({
      seqno,
      sendMode: SendMode.PAY_GAS_SEPARATELY,
      secretKey: keyPair.secretKey,
      messages: [
        internal({
          value: value.toString(),
          to: user.ton_wallet_address,
          body: "Withdrawal from marketplace",
        }),
      ],
    });

    // Send the transaction
    await marketplaceContract.send(transfer);

    console.log(
      `Withdrawal processed: ${netAmountToUser} TON sent to ${user.ton_wallet_address} (${feeAmount} TON fee applied)`
    );

    return {
      success: true,
      message: `Withdrawal successful. ${netAmountToUser} TON sent to your wallet (${feeAmount} TON fee applied)`,
      netAmount: netAmountToUser,
      feeAmount,
      transactionHash: transfer.hash().toString("hex"),
    };
  } catch (error) {
    console.error("Error processing withdrawal:", JSON.stringify(error));
    throw new HttpsError(
      "internal",
      (error as any).message ?? "Server error while processing withdrawal."
    );
  }
});
