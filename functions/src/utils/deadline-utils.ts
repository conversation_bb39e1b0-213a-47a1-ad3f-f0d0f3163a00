import * as admin from "firebase-admin";
import { Collection, CollectionStatus } from "../types";

// 7 days in milliseconds
const DEADLINE_DAYS = 7;
const DEADLINE_MS = DEADLINE_DAYS * 24 * 60 * 60 * 1000;

export async function addDeadlineIfMarketCollection(
  db: admin.firestore.Firestore,
  collectionId: string,
  orderId: string,
  updateData: any
): Promise<void> {
  const collectionDoc = await db.collection("collections").doc(collectionId).get();
  
  if (collectionDoc.exists) {
    const collection = collectionDoc.data() as Collection;
    if (collection.status === CollectionStatus.MARKET) {
      const deadline = new Date(Date.now() + DEADLINE_MS);
      updateData.deadline = admin.firestore.Timestamp.fromDate(deadline);
      console.log(
        `Added 7-day deadline to order ${orderId} for MARKET collection ${collectionId}`
      );
    }
  }
}
