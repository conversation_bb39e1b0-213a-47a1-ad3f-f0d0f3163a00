import { HttpsError, CallableRequest } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import { UserEntity } from "../types";

export interface AuthenticatedRequest extends CallableRequest {
  auth: NonNullable<CallableRequest["auth"]>;
}

export interface ValidatedUserRequest extends AuthenticatedRequest {
  user: UserEntity;
}

/**
 * Validates that the request is authenticated
 */
export function requireAuthentication(request: CallableRequest): AuthenticatedRequest {
  if (!request.auth) {
    throw new HttpsError("unauthenticated", "Authentication required.");
  }
  return request as AuthenticatedRequest;
}

/**
 * Validates that the authenticated user matches the provided userId
 */
export function requireUserPermission(
  request: AuthenticatedRequest,
  userId: string,
  operation: string = "operation"
): void {
  if (request.auth.uid !== userId) {
    throw new HttpsError(
      "permission-denied",
      `You can only perform ${operation} for yourself.`
    );
  }
}

/**
 * Validates input parameters are present and valid
 */
export function validateRequiredParams(
  data: any,
  requiredParams: string[]
): void {
  for (const param of requiredParams) {
    if (!data[param]) {
      throw new HttpsError(
        "invalid-argument",
        `${param} is required.`
      );
    }
  }
}

/**
 * Validates that an amount is positive
 */
export function validatePositiveAmount(amount: number, fieldName: string = "amount"): void {
  if (!amount || amount <= 0) {
    throw new HttpsError(
      "invalid-argument",
      `${fieldName} must be greater than 0.`
    );
  }
}

/**
 * Gets user data from Firestore and validates existence
 */
export async function getUserData(userId: string): Promise<UserEntity> {
  const db = admin.firestore();
  const userDoc = await db.collection("users").doc(userId).get();
  
  if (!userDoc.exists) {
    throw new HttpsError("not-found", "User not found.");
  }

  return { id: userDoc.id, ...userDoc.data() } as UserEntity;
}

/**
 * Validates that user has admin role
 */
export async function requireAdminRole(userId: string): Promise<UserEntity> {
  const user = await getUserData(userId);
  
  if (user.role !== "admin") {
    throw new HttpsError(
      "permission-denied",
      "Only admin users can perform this operation."
    );
  }

  return user;
}

/**
 * Validates that user has a TON wallet address configured
 */
export function requireTonWallet(user: UserEntity): void {
  if (!user.ton_wallet_address) {
    throw new HttpsError(
      "failed-precondition",
      "User does not have a TON wallet address configured."
    );
  }
}

/**
 * Combined authentication and user data retrieval
 */
export async function authenticateAndGetUser(request: CallableRequest): Promise<{
  request: AuthenticatedRequest;
  user: UserEntity;
}> {
  const authRequest = requireAuthentication(request);
  const user = await getUserData(authRequest.auth.uid);
  
  return { request: authRequest, user };
}

/**
 * Validates order ownership for buyer operations
 */
export function validateBuyerOwnership(
  request: AuthenticatedRequest,
  buyerId: string
): void {
  requireUserPermission(request, buyerId, "buyer operations");
}

/**
 * Validates order ownership for seller operations
 */
export function validateSellerOwnership(
  request: AuthenticatedRequest,
  sellerId: string
): void {
  requireUserPermission(request, sellerId, "seller operations");
}

/**
 * Validates order creation parameters
 */
export function validateOrderCreationParams(data: any, userType: 'buyer' | 'seller'): void {
  const userIdField = userType === 'buyer' ? 'buyerId' : 'sellerId';
  
  validateRequiredParams(data, [userIdField, 'collectionId', 'amount']);
  validatePositiveAmount(data.amount);
}

/**
 * Validates purchase parameters
 */
export function validatePurchaseParams(data: any, userType: 'buyer' | 'seller'): void {
  const userIdField = userType === 'buyer' ? 'buyerId' : 'sellerId';
  
  validateRequiredParams(data, [userIdField, 'orderId']);
}
